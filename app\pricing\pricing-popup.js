'use client';

import { useState } from 'react';

export default function PricingPopup({ isOpen, onClose }) {
  const [vehicleSize, setVehicleSize] = useState('');
  const [packageType, setPackageType] = useState('');
  const [petHair, setPetHair] = useState(false);
  const [year, setYear] = useState('');
  const [make, setMake] = useState('');
  const [model, setModel] = useState('');
  const [windowShieldCeramic, setWindowShieldCeramic] = useState(false);
  const [engineClean, setEngineClean] = useState(false);
  const [fullBodyCeramic, setFullBodyCeramic] = useState(false);
  const [scratchRemoval, setScratchRemoval] = useState(0);
  const [headlightRestoration, setHeadlightRestoration] = useState(0);
  const [paintCorrection, setPaintCorrection] = useState(false);

  // Base package prices
  const packagePrices = {
    interior: 239,
    exterior: 99,
    both: 260
  };

  // Vehicle size multipliers
  const sizeMultipliers = {
    sedan: 1.0,
    'small-suv': 1.1,
    'large-suv': 1.15,
    'small-truck': 1.05,
    'large-truck': 1.1,
    van: 1.7
  };

  // Calculate final price
  const calculatePrice = () => {
    if (!vehicleSize || !packageType) return null;

    const basePrice = packagePrices[packageType];
    let finalPrice;

    // Exception: Exterior package uses base price regardless of vehicle size
    if (packageType === 'exterior') {
      finalPrice = basePrice;
    } else {
      const multiplier = sizeMultipliers[vehicleSize];
      finalPrice = basePrice * multiplier;
    }

    // Add all addon fees
    if (petHair) finalPrice += 75;
    if (windowShieldCeramic) finalPrice += 25;
    if (engineClean) finalPrice += 50;
    if (fullBodyCeramic) finalPrice += 600;
    if (paintCorrection) finalPrice += 600;
    finalPrice += scratchRemoval * 200;
    finalPrice += headlightRestoration * 70;

    return Math.round(finalPrice * 100) / 100; // Round to 2 decimal places
  };

  // Calculate prices for all packages for the summary
  const calculateAllPrices = () => {
    if (!vehicleSize) return null;

    const prices = {};
    Object.keys(packagePrices).forEach(pkg => {
      let price = packagePrices[pkg];

      // Apply size multiplier except for exterior
      if (pkg !== 'exterior') {
        price *= sizeMultipliers[vehicleSize];
      }

      prices[pkg] = Math.round(price * 100) / 100;
    });

    return prices;
  };

  const allPrices = calculateAllPrices();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-md max-h-[90vh] overflow-y-auto p-6 relative">
        {/* Close button */}
        <button 
          onClick={onClose} 
          className="absolute top-2 right-2 text-gray-500 hover:text-gray-700 text-xl"
        >
          &times;
        </button>

        {/* Header */}
        <h2 className="text-2xl font-bold text-center text-blue-600 mb-6">
          Pricing Calculator
        </h2>

        {/* Vehicle Details */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Vehicle Details
          </label>
          <div className="grid grid-cols-3 gap-2">
            <input
              type="text"
              placeholder="Year"
              value={year}
              onChange={(e) => setYear(e.target.value)}
              className="p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
            />
            <input
              type="text"
              placeholder="Make"
              value={make}
              onChange={(e) => setMake(e.target.value)}
              className="p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
            />
            <input
              type="text"
              placeholder="Model"
              value={model}
              onChange={(e) => setModel(e.target.value)}
              className="p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
            />
          </div>
        </div>

        {/* Vehicle Size Selection */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Vehicle Size
          </label>
          <select
            value={vehicleSize}
            onChange={(e) => setVehicleSize(e.target.value)}
            className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Select vehicle size...</option>
            <option value="sedan">Sedan (1.0x)</option>
            <option value="small-suv">Small SUV - 2 Rows (1.1x)</option>
            <option value="large-suv">Large SUV - 3 Rows (1.15x)</option>
            <option value="small-truck">Small/Mid Truck (1.05x)</option>
            <option value="large-truck">Large Truck (1.1x)</option>
            <option value="van">Van - 4+ Rows (1.7x)</option>
          </select>
        </div>

        {/* Package Selection */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Package
          </label>
          <select
            value={packageType}
            onChange={(e) => setPackageType(e.target.value)}
            className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Select package...</option>
            <option value="interior">Interior Detailing ($239 base)</option>
            <option value="exterior">Exterior Detailing ($99 base - no size multiplier)</option>
            <option value="both">Interior & Exterior Detailing ($260 base)</option>
          </select>
        </div>

        {/* Pet Hair Option */}
        <div className="mb-6">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={petHair}
              onChange={(e) => setPetHair(e.target.checked)}
              className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <span className="text-sm font-medium text-gray-700">
              Pet Hair Removal (+$75)
            </span>
          </label>
        </div>

        {/* Additional Services */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Additional Services
          </label>
          <div className="space-y-3">
            {/* Window Shield Ceramic Coating */}
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={windowShieldCeramic}
                onChange={(e) => setWindowShieldCeramic(e.target.checked)}
                className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="text-sm text-gray-700">
                Window Shield Ceramic Coating (+$25)
              </span>
            </label>

            {/* Engine Clean */}
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={engineClean}
                onChange={(e) => setEngineClean(e.target.checked)}
                className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="text-sm text-gray-700">
                Engine Clean (+$50)
              </span>
            </label>

            {/* Full Body Ceramic Coating */}
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={fullBodyCeramic}
                onChange={(e) => setFullBodyCeramic(e.target.checked)}
                className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="text-sm text-gray-700">
                Full Body Ceramic Coating (+$600)
              </span>
            </label>

            {/* Paint Correction */}
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={paintCorrection}
                onChange={(e) => setPaintCorrection(e.target.checked)}
                className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="text-sm text-gray-700">
                Paint Correction (+$600)
              </span>
            </label>

            {/* Scratch Removal */}
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-700 min-w-0 flex-1">
                Scratch Removal / Per Body Panel ($200 each):
              </span>
              <input
                type="number"
                min="0"
                max="20"
                value={scratchRemoval}
                onChange={(e) => setScratchRemoval(parseInt(e.target.value) || 0)}
                className="w-16 p-1 border border-gray-300 rounded text-sm text-center"
              />
            </div>

            {/* Headlight Restoration */}
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-700 min-w-0 flex-1">
                Headlight Restoration ($70 per headlight):
              </span>
              <select
                value={headlightRestoration}
                onChange={(e) => setHeadlightRestoration(parseInt(e.target.value))}
                className="w-16 p-1 border border-gray-300 rounded text-sm"
              >
                <option value={0}>0</option>
                <option value={1}>1</option>
                <option value={2}>2</option>
              </select>
            </div>
          </div>
        </div>



        {/* Auto-generated summary */}
        {allPrices && (
          <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex justify-between items-start mb-2">
              <h3 className="font-medium text-blue-900">Pricing Summary</h3>
              <button
                onClick={() => {
                  const vehicleText = (year || make || model) ? `${year && `${year} `}${make && `${make} `}${model}` : 'vehicle';
                  let summaryText = `The pricing for your ${vehicleText} is:\n\nInterior: $${allPrices.interior.toFixed(2)}\nExterior: $${allPrices.exterior.toFixed(2)}\nIn & Out: $${allPrices.both.toFixed(2)}\n`;

                  // Add addons to summary
                  const addons = [];
                  if (petHair) addons.push('Pet Hair Removal: $75');
                  if (windowShieldCeramic) addons.push('Window Shield Ceramic Coating: $25');
                  if (engineClean) addons.push('Engine Clean: $50');
                  if (fullBodyCeramic) addons.push('Full Body Ceramic Coating: $600');
                  if (paintCorrection) addons.push('Paint Correction: $600');
                  if (scratchRemoval > 0) addons.push(`Scratch Removal (${scratchRemoval} panels): $${scratchRemoval * 200}`);
                  if (headlightRestoration > 0) addons.push(`Headlight Restoration (${headlightRestoration} headlight${headlightRestoration > 1 ? 's' : ''}): $${headlightRestoration * 70}`);

                  if (addons.length > 0) {
                    summaryText += '\nAdd-ons:\n' + addons.join('\n');
                  } else {
                    summaryText += 'Pet Hair removal is extra';
                  }

                  navigator.clipboard.writeText(summaryText).then(() => {
                    alert('Pricing summary copied to clipboard!');
                  }).catch(() => {
                    alert('Failed to copy to clipboard');
                  });
                }}
                className="px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 transition-colors"
                title="Copy pricing summary"
              >
                Copy
              </button>
            </div>
            <p className="text-sm text-blue-800 mb-3">
              The pricing for your {(year || make || model) ? `${year && `${year} `}${make && `${make} `}${model}` : 'vehicle'} is:
            </p>
            <div className="text-sm text-blue-700 space-y-1">
              <div>Interior: ${allPrices.interior.toFixed(2)}</div>
              <div>Exterior: ${allPrices.exterior.toFixed(2)}</div>
              <div>In & Out: ${allPrices.both.toFixed(2)}</div>

              {/* Show selected addons */}
              {(petHair || windowShieldCeramic || engineClean || fullBodyCeramic || paintCorrection || scratchRemoval > 0 || headlightRestoration > 0) && (
                <div className="mt-3 pt-2 border-t border-blue-200">
                  <div className="text-xs text-blue-800 font-medium mb-1">Add-ons:</div>
                  {petHair && <div className="text-xs">Pet Hair Removal: $75</div>}
                  {windowShieldCeramic && <div className="text-xs">Window Shield Ceramic Coating: $25</div>}
                  {engineClean && <div className="text-xs">Engine Clean: $50</div>}
                  {fullBodyCeramic && <div className="text-xs">Full Body Ceramic Coating: $600</div>}
                  {paintCorrection && <div className="text-xs">Paint Correction: $600</div>}
                  {scratchRemoval > 0 && <div className="text-xs">Scratch Removal ({scratchRemoval} panels): ${scratchRemoval * 200}</div>}
                  {headlightRestoration > 0 && <div className="text-xs">Headlight Restoration ({headlightRestoration} headlight{headlightRestoration > 1 ? 's' : ''}): ${headlightRestoration * 70}</div>}
                </div>
              )}

              {/* Show "Pet Hair removal is extra" only if no addons are selected */}
              {!(petHair || windowShieldCeramic || engineClean || fullBodyCeramic || paintCorrection || scratchRemoval > 0 || headlightRestoration > 0) && (
                <div className="text-xs text-blue-600 mt-2 italic">
                  Pet Hair removal is extra
                </div>
              )}
            </div>
          </div>
        )}

        {/* Reset button */}
        {(vehicleSize || packageType || petHair || year || make || model || windowShieldCeramic || engineClean || fullBodyCeramic || paintCorrection || scratchRemoval > 0 || headlightRestoration > 0) && (
          <div className="mt-4 text-center">
            <button
              onClick={() => {
                setVehicleSize('');
                setPackageType('');
                setPetHair(false);
                setYear('');
                setMake('');
                setModel('');
                setWindowShieldCeramic(false);
                setEngineClean(false);
                setFullBodyCeramic(false);
                setPaintCorrection(false);
                setScratchRemoval(0);
                setHeadlightRestoration(0);
              }}
              className="text-blue-600 hover:text-blue-700 text-sm underline"
            >
              Reset all
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
