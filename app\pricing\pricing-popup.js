'use client';

import { useState } from 'react';

export default function PricingPopup({ isOpen, onClose }) {
  const [vehicleSize, setVehicleSize] = useState('');
  const [packageType, setPackageType] = useState('');
  const [petHair, setPetHair] = useState(false);
  const [year, setYear] = useState('');
  const [make, setMake] = useState('');
  const [model, setModel] = useState('');

  // Base package prices
  const packagePrices = {
    interior: 239,
    exterior: 99,
    both: 260
  };

  // Vehicle size multipliers
  const sizeMultipliers = {
    sedan: 1.0,
    'small-suv': 1.1,
    'large-suv': 1.15,
    'small-truck': 1.05,
    'large-truck': 1.1,
    van: 1.7
  };

  // Calculate final price
  const calculatePrice = () => {
    if (!vehicleSize || !packageType) return null;

    const basePrice = packagePrices[packageType];
    let finalPrice;

    // Exception: Exterior package uses base price regardless of vehicle size
    if (packageType === 'exterior') {
      finalPrice = basePrice;
    } else {
      const multiplier = sizeMultipliers[vehicleSize];
      finalPrice = basePrice * multiplier;
    }

    // Add pet hair removal fee if selected
    if (petHair) {
      finalPrice += 75;
    }

    return Math.round(finalPrice * 100) / 100; // Round to 2 decimal places
  };

  // Calculate prices for all packages for the summary
  const calculateAllPrices = () => {
    if (!vehicleSize) return null;

    const prices = {};
    Object.keys(packagePrices).forEach(pkg => {
      let price = packagePrices[pkg];

      // Apply size multiplier except for exterior
      if (pkg !== 'exterior') {
        price *= sizeMultipliers[vehicleSize];
      }

      prices[pkg] = Math.round(price * 100) / 100;
    });

    return prices;
  };

  const finalPrice = calculatePrice();
  const allPrices = calculateAllPrices();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-6 relative">
        {/* Close button */}
        <button 
          onClick={onClose} 
          className="absolute top-2 right-2 text-gray-500 hover:text-gray-700 text-xl"
        >
          &times;
        </button>

        {/* Header */}
        <h2 className="text-2xl font-bold text-center text-blue-600 mb-6">
          Pricing Calculator
        </h2>

        {/* Vehicle Details */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Vehicle Details
          </label>
          <div className="grid grid-cols-3 gap-2">
            <input
              type="text"
              placeholder="Year"
              value={year}
              onChange={(e) => setYear(e.target.value)}
              className="p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
            />
            <input
              type="text"
              placeholder="Make"
              value={make}
              onChange={(e) => setMake(e.target.value)}
              className="p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
            />
            <input
              type="text"
              placeholder="Model"
              value={model}
              onChange={(e) => setModel(e.target.value)}
              className="p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
            />
          </div>
        </div>

        {/* Vehicle Size Selection */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Vehicle Size
          </label>
          <select
            value={vehicleSize}
            onChange={(e) => setVehicleSize(e.target.value)}
            className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Select vehicle size...</option>
            <option value="sedan">Sedan (1.0x)</option>
            <option value="small-suv">Small SUV - 2 Rows (1.1x)</option>
            <option value="large-suv">Large SUV - 3 Rows (1.15x)</option>
            <option value="small-truck">Small/Mid Truck (1.05x)</option>
            <option value="large-truck">Large Truck (1.1x)</option>
            <option value="van">Van - 4+ Rows (1.7x)</option>
          </select>
        </div>

        {/* Package Selection */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Package
          </label>
          <select
            value={packageType}
            onChange={(e) => setPackageType(e.target.value)}
            className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Select package...</option>
            <option value="interior">Interior Detailing ($239 base)</option>
            <option value="exterior">Exterior Detailing ($99 base - no size multiplier)</option>
            <option value="both">Interior & Exterior Detailing ($260 base)</option>
          </select>
        </div>

        {/* Pet Hair Option */}
        <div className="mb-6">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={petHair}
              onChange={(e) => setPetHair(e.target.checked)}
              className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <span className="text-sm font-medium text-gray-700">
              Pet Hair Removal (+$75)
            </span>
          </label>
        </div>

        {/* Price Display */}
        <div className="bg-gray-50 rounded-lg p-6 text-center">
          {finalPrice !== null ? (
            <>
              <div className="text-3xl font-bold text-green-600 mb-2">
                ${finalPrice.toFixed(2)}
              </div>
              <div className="text-sm text-gray-600">
                {packageType === 'exterior' ? (
                  <span>
                    Base price (no size multiplier for exterior detailing)
                    {petHair && <span> + $75 pet hair removal</span>}
                  </span>
                ) : (
                  <span>
                    ${packagePrices[packageType]} × {sizeMultipliers[vehicleSize]} = ${(packagePrices[packageType] * sizeMultipliers[vehicleSize]).toFixed(2)}
                    {petHair && <span> + $75 pet hair removal</span>}
                  </span>
                )}
              </div>
            </>
          ) : (
            <div className="text-gray-500">
              Select vehicle size and package to see price
            </div>
          )}
        </div>

        {/* Auto-generated summary */}
        {allPrices && (year || make || model) && (
          <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h3 className="font-medium text-blue-900 mb-2">Pricing Summary</h3>
            <p className="text-sm text-blue-800 mb-3">
              The pricing for your {year && `${year} `}{make && `${make} `}{model} is:
            </p>
            <div className="text-sm text-blue-700 space-y-1">
              <div>Interior: ${allPrices.interior.toFixed(2)}</div>
              <div>Exterior: ${allPrices.exterior.toFixed(2)}</div>
              <div>In & Out: ${allPrices.both.toFixed(2)}</div>
              <div className="text-xs text-blue-600 mt-2 italic">
                Pet Hair removal is extra (+$75)
              </div>
            </div>
          </div>
        )}

        {/* Reset button */}
        {(vehicleSize || packageType || petHair || year || make || model) && (
          <div className="mt-4 text-center">
            <button
              onClick={() => {
                setVehicleSize('');
                setPackageType('');
                setPetHair(false);
                setYear('');
                setMake('');
                setModel('');
              }}
              className="text-blue-600 hover:text-blue-700 text-sm underline"
            >
              Reset all
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
