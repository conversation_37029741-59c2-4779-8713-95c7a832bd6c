'use client';

import { useState } from 'react';

export default function PricingPopup({ isOpen, onClose }) {
  const [vehicleSize, setVehicleSize] = useState('');
  const [packageType, setPackageType] = useState('');

  // Base package prices
  const packagePrices = {
    interior: 100,
    exterior: 80,
    both: 150
  };

  // Vehicle size multipliers
  const sizeMultipliers = {
    sedan: 1.0,
    'small-suv': 1.1,
    'large-suv': 1.15,
    'small-truck': 1.05,
    'large-truck': 1.1,
    van: 1.7
  };

  // Calculate final price
  const calculatePrice = () => {
    if (!vehicleSize || !packageType) return null;
    
    const basePrice = packagePrices[packageType];
    
    // Exception: Exterior package uses base price regardless of vehicle size
    if (packageType === 'exterior') {
      return basePrice;
    }
    
    const multiplier = sizeMultipliers[vehicleSize];
    return Math.round(basePrice * multiplier);
  };

  const finalPrice = calculatePrice();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-6 relative">
        {/* Close button */}
        <button 
          onClick={onClose} 
          className="absolute top-2 right-2 text-gray-500 hover:text-gray-700 text-xl"
        >
          &times;
        </button>

        {/* Header */}
        <h2 className="text-2xl font-bold text-center text-blue-600 mb-6">
          Pricing Calculator
        </h2>

        {/* Vehicle Size Selection */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Vehicle Size
          </label>
          <select
            value={vehicleSize}
            onChange={(e) => setVehicleSize(e.target.value)}
            className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Select vehicle size...</option>
            <option value="sedan">Sedan (1.0x)</option>
            <option value="small-suv">Small SUV - 2 Rows (1.1x)</option>
            <option value="large-suv">Large SUV - 3 Rows (1.15x)</option>
            <option value="small-truck">Small/Mid Truck (1.05x)</option>
            <option value="large-truck">Large Truck (1.1x)</option>
            <option value="van">Van - 4+ Rows (1.7x)</option>
          </select>
        </div>

        {/* Package Selection */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Package
          </label>
          <select
            value={packageType}
            onChange={(e) => setPackageType(e.target.value)}
            className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Select package...</option>
            <option value="interior">Interior Only ($100 base)</option>
            <option value="exterior">Exterior Only ($80 base - no size multiplier)</option>
            <option value="both">Interior + Exterior ($150 base)</option>
          </select>
        </div>

        {/* Price Display */}
        <div className="bg-gray-50 rounded-lg p-6 text-center">
          {finalPrice !== null ? (
            <>
              <div className="text-3xl font-bold text-green-600 mb-2">
                ${finalPrice}
              </div>
              <div className="text-sm text-gray-600">
                {packageType === 'exterior' ? (
                  <span>Base price (no size multiplier for exterior)</span>
                ) : (
                  <span>
                    ${packagePrices[packageType]} × {sizeMultipliers[vehicleSize]} = ${finalPrice}
                  </span>
                )}
              </div>
            </>
          ) : (
            <div className="text-gray-500">
              Select vehicle size and package to see price
            </div>
          )}
        </div>

        {/* Reset button */}
        {(vehicleSize || packageType) && (
          <div className="mt-4 text-center">
            <button
              onClick={() => {
                setVehicleSize('');
                setPackageType('');
              }}
              className="text-blue-600 hover:text-blue-700 text-sm underline"
            >
              Reset selections
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
