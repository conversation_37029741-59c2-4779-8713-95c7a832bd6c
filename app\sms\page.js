'use client';

import React, { useState, useEffect } from 'react';
import { onAuthStateChangedListener } from '../../auth';
import { doc, getDoc, collection, onSnapshot } from 'firebase/firestore';
import { db } from '../../lib/firebase/firebase';
import MessengerPopup from './sms-pop';
import ContactPopup from './contact-popup';

export default function MessengerPage() {
  const [contacts, setContacts] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [locationDetails, setLocationDetails] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [userDoc, setUserDoc] = useState(null);
  const [selectedContact, setSelectedContact] = useState(null);
  const [showConversation, setShowConversation] = useState(false);
  const [filterMode, setFilterMode] = useState('all');
  const [userName, setUserName] = useState('');
  const [showContactPopup, setShowContactPopup] = useState(false);
  const formatPhoneNumber = (phone) => {
    if (!phone) return '';
    const cleaned = phone.replace(/\D/g, '');
    const match = cleaned.match(/(\d{4})$/);
    return match ? `•••• ${match[1]}` : '••••';
  };

  useEffect(() => {
    const unsubscribe = onAuthStateChangedListener(async (user) => {
      if (user) {
        try {
          const docRef = doc(db, 'users', user.uid);
          const docSnap = await getDoc(docRef);
          if (docSnap.exists()) {
            const userData = docSnap.data();
            setUserDoc(userData);
            setUserName(user.displayName || '');

            const branch = userData.branch || '';
            const response = await fetch(
              `https://dotg-team-calendar-************.us-central1.run.app?branch=${encodeURIComponent(branch)}`
            );
            const text = await response.text();
            console.log('Cloud function response:', text);

            if (!response.ok) {
              throw new Error(`Failed to fetch data: ${response.status}`);
            }

            const data = JSON.parse(text);
            if (!data.collectionId) {
              throw new Error('No collectionId returned from backend');
            }

            setLocationDetails({
              businessNumber: data.businessNumber || '', // Explicitly from Firestore
              collectionId: data.collectionId, // From backend
              location: branch,
            });
          } else {
            throw new Error('User document not found');
          }
        } catch (error) {
          console.error('Error setting up messenger:', error);
          setUserDoc(null);
          setLocationDetails(null);
          setUserName('');
        }
      } else {
        setUserDoc(null);
        setLocationDetails(null);
        setUserName('');
      }
      setIsLoading(false);
    });

    return () => unsubscribe();
  }, []);

  useEffect(() => {
    if (!locationDetails?.collectionId) return;

    const smsCollection = collection(db, locationDetails.collectionId);
    const unsubscribe = onSnapshot(smsCollection, (snapshot) => {
      const contactsArr = [];
      snapshot.forEach((docSnap) => {
        const data = docSnap.data();
        const allMsgs = data.messages || [];

        allMsgs.sort((a, b) => {
          const aTime = (a.timestamp?.seconds || 0) * 1000;
          const bTime = (b.timestamp?.seconds || 0) * 1000;
          return bTime - aTime;
        });

        const lastMessage = allMsgs[0] || {};
        const lastMsgTimestamp = lastMessage.timestamp
          ? new Date(lastMessage.timestamp.seconds * 1000).toLocaleString()
          : null;

        const isUnread = lastMessage.direction === 'incoming';

        contactsArr.push({
          id: docSnap.id,
          name: `${data.firstName || ''} ${data.lastName || ''}`.trim(),
          phone: docSnap.id,
          lastMessage: lastMessage.message || 'No messages yet',
          lastMsgTimestamp,
          lastMsgRawMillis: lastMessage.timestamp
            ? lastMessage.timestamp.seconds * 1000
            : 0,
          isUnread,
        });
      });

      contactsArr.sort((a, b) => b.lastMsgRawMillis - a.lastMsgRawMillis);
      setContacts(contactsArr);
    });

    return () => unsubscribe();
  }, [locationDetails]);

  const handleMarkAsRead = (contact) => {
    setContacts((prev) =>
      prev.map((c) => (c.id === contact.id ? { ...c, isUnread: false } : c))
    );

    fetch(
      `https://us-central1-detail-on-the-go-universal.cloudfunctions.net/read-text?to=${encodeURIComponent(
        contact.phone
      )}&from=${encodeURIComponent(locationDetails?.businessNumber || '')}&message=${encodeURIComponent(
        'Read'
      )}&delay=0`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      }
    ).catch((err) => {
      console.error('Error marking as read:', err);
    });
  };

  const filteredContacts = contacts
    .filter((c) =>
      c.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      c.phone.includes(searchQuery)
    )
    .filter((c) => (filterMode === 'unread' ? c.isUnread : true));

  const handleSelectContact = (contact) => {
    console.log('Selected contact:', contact);
    setSelectedContact(contact);
    setShowConversation(true);
  };

  return (
    <div className="min-h-screen bg-gray-50 pt-3 pb-4 px-2 sm:px-4 sm:pt-20">
      {isLoading ? (
        <div className="flex justify-center items-center h-screen">
          <p>Loading...</p>
        </div>
      ) : !locationDetails ? (
        <div className="flex justify-center items-center h-screen">
          <p>Contact admin to view this page</p>
        </div>
      ) : (
        <>
          {locationDetails && (
            <div className="mb-6 text-gray-700">
              <h2 className="font-bold text-lg">{userName}</h2>
              <p className="text-sm">{locationDetails.location}</p>
            </div>
          )}

          <div className="flex flex-wrap items-center gap-2 mb-4">
            <input
              placeholder="Search contacts..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="p-3 flex-1 border rounded-lg shadow-sm text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <select
              value={filterMode}
              onChange={(e) => setFilterMode(e.target.value)}
              className="p-2 border rounded-md"
            >
              <option value="all">All Contacts</option>
              <option value="unread">Unread Only</option>
            </select>
            <button
              onClick={() => setShowContactPopup(true)}
              className="px-4 py-2 bg-green-600 text-white rounded-md"
            >
              Add Contact
            </button>
          </div>

          <div className="space-y-2 overflow-y-auto">
            {filteredContacts.map((contact) => {
              const selectedClass =
                selectedContact?.id === contact.id
                  ? 'border-l-4 border-blue-600 bg-blue-50'
                  : 'hover:bg-gray-100';

              return (
                <div
                  key={contact.id}
                  className={`relative p-4 rounded-lg cursor-pointer transition-colors ${selectedClass}`}
                  onClick={() => handleSelectContact(contact)}
                >
                  {contact.isUnread && (
                    <div className="absolute inset-0 bg-red-200 animate-pulse pointer-events-none" />
                  )}
                  <div className="relative z-10">
                    <h3 className="font-medium text-gray-900">
                      {contact.name ? (
                        contact.name
                      ) : (
                        <span className="text-gray-900 bg-gray-100/50 border border-gray-300 px-2 py-1 rounded">
                          Unknown
                        </span>
                      )}
                    </h3>
                    <p className="text-sm text-gray-600">{formatPhoneNumber(contact.phone)}</p>
                    <p className="text-sm text-gray-700 mt-1 font-medium">
                      {contact.lastMessage}
                    </p>
                    {contact.isUnread && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleMarkAsRead(contact);
                        }}
                        className="mt-2 px-3 py-1 bg-blue-600 text-white rounded-md"
                      >
                        Read
                      </button>
                    )}
                  </div>
                </div>
              );
            })}
          </div>

          {showConversation && selectedContact && (
            <MessengerPopup
              isOpen={showConversation}
              onClose={() => {
                setShowConversation(false);
                setSelectedContact(null);
              }}
              businessNumber={locationDetails?.businessNumber || ''}
              clientNumber={selectedContact.phone}
              location={locationDetails?.location || ''} // Add this
            />
          )}

          {showContactPopup && (
            <ContactPopup
              isOpen={showContactPopup}
              onClose={() => setShowContactPopup(false)}
              businessNumber={locationDetails?.businessNumber || ''}
            />
          )}
        </>
      )}
    </div>
  );
}